2025-08-14T18:33:59.973+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/nanjing-dev failed!err:open ./nacos/reg/naming/nanjing-dev: no such file or directory
2025-08-14T18:33:59.973+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55107
2025-08-14T18:33:59.973+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=4ae3a8d7-b4d2-455d-a188-cd65570a50e5)
2025-08-14T18:33:59.973+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-08-14T18:33:59.973+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-08-14T18:33:59.973+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-08-14T18:33:59.973+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 4ae3a8d7-b4d2-455d-a188-cd65570a50e5 try to connect to server on start up, server: {serverIp:************ serverPort:8848 serverGrpcPort:9848}
2025-08-14T18:33:59.974+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-08-14T18:33:59.974+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-08-14T18:33:59.974+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-08-14T18:33:59.974+0800	INFO	util/common.go:96	Local IP:************
2025-08-14T18:34:00.086+0800	INFO	rpc/rpc_client.go:337	4ae3a8d7-b4d2-455d-a188-cd65570a50e5 success to connect to server {serverIp:************ serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1755167642152_192.168.3.3_64507
2025-08-14T18:34:00.086+0800	INFO	rpc/rpc_client.go:486	4ae3a8d7-b4d2-455d-a188-cd65570a50e5 notify connected event to listeners , connectionId=1755167642152_192.168.3.3_64507
