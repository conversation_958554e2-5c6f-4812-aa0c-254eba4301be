package crawl

import (
	"assetManagementService/internal/model/crawl"
	"assetManagementService/internal/model/data_saver"
	"assetManagementService/internal/model/website"
	"assetManagementService/internal/service"
	"context"
	"fmt"
	"net/url"
	"strings"
	"time"

	md "github.com/JohannesKaufmann/html-to-markdown"
	"github.com/gogf/gf/v2/container/gmap"
	"github.com/gogf/gf/v2/encoding/gbase64"
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gctx"
	"github.com/gogf/gf/v2/os/gfile"
	"github.com/gogf/gf/v2/os/grpool"
	"github.com/playwright-community/playwright-go"
)

// sPlaywrightCrawler Playwright 爬蟲實現
type sPlaywrightCrawler struct {
	playWright *playwright.Playwright
	workerPool *grpool.Pool
}

// newPlaywrightCrawler 創建 Playwright 爬蟲實例
func newPlaywrightCrawler(ctx context.Context) service.ICrawler {
	s := &sPlaywrightCrawler{
		workerPool: grpool.New(),
	}
	var err error
	s.playWright, err = playwright.Run()
	if err != nil {
		logger().Errorf(ctx, "Failed to run Playwright: %v", err)
		panic(err)
	}
	logger().Infof(ctx, "Playwright crawler initialized successfully")
	return s
}

// createPage 在瀏覽器上下文中創建新頁面，並設置反爬蟲設置
func (s *sPlaywrightCrawler) createPage(ctx context.Context, pageCtx playwright.BrowserContext, pageURL string, config *AntiBotConfig) (playwright.Page, error) {
	page, err := pageCtx.NewPage()
	if err != nil {
		logger().Errorf(ctx, "Error creating page for %s: %v", pageURL, err)
		return nil, fmt.Errorf("failed to create page: %w", err)
	}

	// 設置額外的 HTTP headers 來模擬真實瀏覽器
	headers := map[string]string{
		"Accept":                    "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
		"Accept-Language":           "zh-TW,zh;q=0.9,en;q=0.8,zh-CN;q=0.7",
		"Accept-Encoding":           "gzip, deflate, br",
		"Cache-Control":             "max-age=0",
		"Sec-Fetch-Dest":            "document",
		"Sec-Fetch-Mode":            "navigate",
		"Sec-Fetch-Site":            "none",
		"Sec-Fetch-User":            "?1",
		"Upgrade-Insecure-Requests": "1",
	}

	// 設置 headers
	if err := page.SetExtraHTTPHeaders(headers); err != nil {
		logger().Errorf(ctx, "Failed to set extra HTTP headers for %s: %v", pageURL, err)
	}

	// 記錄使用的配置信息
	logger().Debugf(ctx, "Created page for %s with anti-bot config (retries: %d, delay: %d-%d ms)",
		pageURL, config.MaxRetries, config.MinDelay, config.MaxDelay)

	return page, nil
}

// navigateToURL 導航到指定 URL，帶有重試機制以防反爬蟲保護
func (s *sPlaywrightCrawler) navigateToURL(ctx context.Context, page playwright.Page, pageURL string, config *AntiBotConfig) error {
	var lastErr error

	for attempt := 0; attempt <= config.MaxRetries; attempt++ {
		if attempt > 0 {
			logger().Infof(ctx, "Retrying navigation to %s (attempt %d/%d)", pageURL, attempt, config.MaxRetries)
			time.Sleep(time.Duration(config.RetryDelay) * time.Millisecond)
		}

		resp, err := page.Goto(pageURL, playwright.PageGotoOptions{
			WaitUntil: playwright.WaitUntilStateNetworkidle,
			Timeout:   playwright.Float(30000), // 30 seconds timeout
		})

		if err != nil {
			lastErr = fmt.Errorf("failed to navigate to URL: %w", err)
			logger().Errorf(ctx, "Error navigating to %s (attempt %d): %v", pageURL, attempt+1, err)
			continue
		}

		// 檢查頁面是否成功載入
		statusCode := resp.Status()
		if statusCode >= 400 {
			lastErr = fmt.Errorf("page returned status code %d", statusCode)
			logger().Errorf(ctx, "Error: %s returned status code %d (attempt %d)", pageURL, statusCode, attempt+1)

			// 對於 403/429 錯誤，特別處理
			if statusCode == 403 || statusCode == 429 {
				logger().Infof(ctx, "Detected anti-bot response (status %d), will retry with different settings", statusCode)
				continue
			}

			// 對於其他 4xx/5xx 錯誤，如果不是最後一次嘗試則繼續重試
			if attempt < config.MaxRetries {
				continue
			}
		} else {
			// 成功載入頁面，檢查內容類型
			logger().Debugf(ctx, "Successfully navigated to %s (status %d)", pageURL, statusCode)

			// 獲取響應的 Content-Type
			headers := resp.Headers()
			contentType := ""
			if headers != nil {
				if ct, exists := headers["content-type"]; exists {
					contentType = ct
				}
			}

			// 檢查是否為網頁內容
			if !isWebContent(contentType) {
				logger().Infof(ctx, "Skipping non-web content: %s (Content-Type: %s)", pageURL, contentType)
				return ErrNonWebContent
			}

			logger().Debugf(ctx, "Content-Type verified as web content: %s", contentType)
			return nil
		}
	}

	return lastErr
}

// getPageContent 獲取頁面的 HTML 內容並轉換為 Markdown
func (s *sPlaywrightCrawler) getPageContent(ctx context.Context, page playwright.Page, pageURL string) (string, error) {
	// 獲取 HTML 內容
	content, err := page.Content()
	if err != nil {
		logger().Errorf(ctx, "Error getting content from %s: %v", pageURL, err)
		return "", fmt.Errorf("failed to get page content: %w", err)
	}

	// 將 HTML 轉換為 Markdown
	converter := md.NewConverter("", true, nil)
	markdown, err := converter.ConvertString(content)
	if err != nil {
		logger().Errorf(ctx, "Error converting HTML to Markdown for %s: %v", pageURL, err)
		return "", fmt.Errorf("failed to convert HTML to Markdown: %w", err)
	}

	return markdown, nil
}

// saveMarkdownToFile 將 Markdown 內容保存到文件並返回文件名
func (s *sPlaywrightCrawler) saveMarkdownToFile(ctx context.Context, markdown, pageURL, outputDir string, visitedURLsCount int) (string, error) {
	filename := urlToFilename(pageURL, outputDir, visitedURLsCount)

	err := gfile.PutContents(filename, markdown)
	if err != nil {
		logger().Errorf(ctx, "Error saving Markdown for %s: %v", pageURL, err)
		return "", fmt.Errorf("failed to save Markdown to file: %w", err)
	}

	logger().Debugf(ctx, "Saved Markdown to %s", filename)
	return filename, nil
}

// extractLinks 從頁面中提取屬於同一域名且尚未訪問的所有鏈接
func (s *sPlaywrightCrawler) extractLinks(page playwright.Page, domainName string, visitedURLs map[string]bool) ([]string, error) {
	// 驗證輸入
	if page == nil {
		return nil, fmt.Errorf("page is nil")
	}
	if domainName == "" {
		return nil, fmt.Errorf("domain name is empty")
	}
	if visitedURLs == nil {
		return nil, fmt.Errorf("visitedURLs map is nil")
	}

	// 執行 JavaScript 獲取所有鏈接
	linksObj, err := page.Evaluate(`
		Array.from(document.querySelectorAll('a[href]'))
			.map(a => a.href)
			.filter(href => href.includes('` + domainName + `'))
	`)
	if err != nil {
		return nil, fmt.Errorf("failed to evaluate JavaScript: %w", err)
	}

	// 將結果轉換為字符串切片
	linksArr, ok := linksObj.([]interface{})
	if !ok {
		return nil, fmt.Errorf("unexpected type for links: %T", linksObj)
	}

	var links []string
	for _, linkObj := range linksArr {
		linkStr, ok := linkObj.(string)
		if !ok {
			// 跳過非字符串鏈接
			continue
		}

		// 標準化 URL
		parsedURL, err := url.Parse(linkStr)
		if err != nil {
			// 跳過無效 URL
			continue
		}

		// 跳過片段鏈接（同一頁面）
		parsedURL.Fragment = ""

		// 跳過不是同一域名的鏈接
		if !strings.Contains(parsedURL.Host, domainName) {
			continue
		}

		normalizedURL := parsedURL.String()

		// 跳過已訪問的鏈接
		if !visitedURLs[normalizedURL] {
			links = append(links, normalizedURL)
		}
	}

	return links, nil
}

// crawlPage 爬取單個頁面，提取其內容，並跟隨鏈接到其他頁面
func (s *sPlaywrightCrawler) crawlPage(
	ctx context.Context,
	pageCtx playwright.BrowserContext,
	pageURL, outputDir string,
	pageToFile *gmap.StrStrMap,
	baseDomainName string,
	visitedURLs map[string]bool,
	depth int,
	maxDepth int,
	maxPages int,
	config *AntiBotConfig,
) {
	// 如果已訪問則跳過
	if visitedURLs[pageURL] {
		return
	}
	visitedURLs[pageURL] = true
	if depth > maxDepth {
		logger().Debugf(ctx, "current depth %d over max depth %d then return", depth, maxDepth)
		return
	}
	if len(visitedURLs) > maxPages {
		logger().Debugf(ctx, "current page count %d over max page count %d then return", len(visitedURLs), maxPages)
		return
	}

	logger().Debugf(ctx, "Crawling: %s", pageURL)

	// 添加隨機延遲以避免被檢測為機器人
	randomDelay(ctx, config)

	pageTimeoutCtx, cancel := context.WithTimeout(ctx, 2*time.Minute)
	defer cancel()

	// 使用反爬蟲設置創建新頁面
	page, err := s.createPage(pageTimeoutCtx, pageCtx, pageURL, config)
	if err != nil {
		return
	}
	defer page.Close()

	// 使用重試機制導航到 URL
	if err := s.navigateToURL(pageTimeoutCtx, page, pageURL, config); err != nil {
		// 如果是非網頁內容，記錄信息並跳過，但不視為錯誤
		if err == ErrNonWebContent {
			logger().Infof(ctx, "Skipping non-web content URL: %s", pageURL)
			return
		}
		// 其他錯誤則記錄並返回
		logger().Errorf(ctx, "Failed to navigate to %s after retries: %v", pageURL, err)
		return
	}

	// 獲取頁面內容作為 Markdown
	markdown, err := s.getPageContent(pageTimeoutCtx, page, pageURL)
	if err != nil {
		return
	}

	// 將 Markdown 保存到文件
	filename, err := s.saveMarkdownToFile(pageTimeoutCtx, markdown, pageURL, outputDir, len(visitedURLs))
	if err != nil {
		return
	}

	// 存儲從 URL 到文件名的映射
	pageToFile.Set(pageURL, filename)

	// 從頁面提取鏈接
	links, err := s.extractLinks(page, baseDomainName, visitedURLs)
	if err != nil {
		logger().Errorf(pageTimeoutCtx, "Error extracting links from %s: %v", pageURL, err)
		return
	}

	// 爬取每個鏈接
	for _, link := range links {
		s.crawlPage(pageTimeoutCtx, pageCtx, link, outputDir, pageToFile, baseDomainName, visitedURLs, depth+1, maxDepth, maxPages, config)
		depth = 0
	}
}

// CrawlWebSites 實現 ICrawler 接口的 CrawlWebSites 方法
func (s *sPlaywrightCrawler) CrawlWebSites(ctx context.Context, in *crawl.CrawlWebSiteInput) (err error) {
	logger().Infof(ctx, "crawl web site : %v", gjson.New(in).MustToJsonIndentString())
	if in == nil {
		return fmt.Errorf("input is nil")
	}

	if len(in.WebPageUrls) > 0 {
		var chs = make(chan *website.ModelWebSite, len(in.WebPageUrls))
		var counter = 0
		g.Go(gctx.NeverDone(ctx), func(ctx context.Context) {
			var allWebSiteData = make([]*website.ModelWebSite, 0)
			var quit = false
			for {
				select {
				case webSiteData := <-chs:
					if webSiteData != nil {
						allWebSiteData = append(allWebSiteData, webSiteData)
					}

					counter++
					if counter == len(in.WebPageUrls) {
						logger().Debugf(ctx, "crawl web sites finish...")
						quit = true
						break
					}
				}

				if quit {
					break
				}
			}

			if allWebSiteData != nil && len(allWebSiteData) > 0 {
				e := service.DataSaver().SaveURLContent(ctx, &data_saver.SaveWebPageInput{
					BaseInfo:    in.BaseInfo,
					WebSiteURLs: allWebSiteData,
				})

				if e != nil {
					panic(e)
				}
			}
		}, func(ctx context.Context, exception error) {
			logger().Error(ctx, exception)
			close(chs)
		})

		for _, siteDataWebSite := range in.WebPageUrls {
			if e := s.workerPool.AddWithRecover(gctx.NeverDone(ctx), func(ctx context.Context) {
				webSite, e := s.crawlWebSite(ctx, &crawl.CrawlingWebSiteInput{
					BaseInfo:   in.BaseInfo,
					WebPageUrl: siteDataWebSite,
				})

				if e != nil {
					chs <- nil
				} else {
					chs <- webSite
				}
			}, func(ctx context.Context, exception error) {
				logger().Error(ctx, exception)
			}); e != nil {
				logger().Debugf(ctx, "crawling web site %q failed : %v", siteDataWebSite.URL, e)
				chs <- nil
			}
		}
	} else {
		err = gerror.New("the web site data is empty")
		return
	}

	return
}

// crawlWebSite 爬取單個網站
func (s *sPlaywrightCrawler) crawlWebSite(ctx context.Context, in *crawl.CrawlingWebSiteInput) (webSiteData *website.ModelWebSite, err error) {
	logger().Infof(ctx, "Crawling website : %v", gjson.New(in).MustToJsonIndentString())

	webSiteData = &website.ModelWebSite{DataWebSite: in.WebPageUrl}

	var pageToFile = gmap.NewStrStrMap()

	defer func() {
		if err == nil {
			webSiteData.WebPageFile = pageToFile.Map()
		}
	}()

	// 載入反爬蟲配置
	config := loadAntiBotConfig(ctx)

	// 增強的瀏覽器啟動參數，用於規避反爬蟲檢測
	launchArgs := []string{
		"--disable-blink-features=AutomationControlled",
		"--disable-web-security",
		"--disable-features=VizDisplayCompositor",
		"--disable-background-timer-throttling",
		"--disable-backgrounding-occluded-windows",
		"--disable-renderer-backgrounding",
		"--disable-field-trial-config",
		"--disable-back-forward-cache",
		"--disable-background-networking",
		"--enable-features=NetworkService,NetworkServiceInProcess",
		"--disable-ipc-flooding-protection",
		"--disable-hang-monitor",
		"--disable-client-side-phishing-detection",
		"--disable-popup-blocking",
		"--disable-prompt-on-repost",
		"--disable-sync",
		"--disable-extensions",
		"--no-first-run",
		"--no-default-browser-check",
		"--no-sandbox",
		"--disable-setuid-sandbox",
		"--disable-dev-shm-usage",
		"--disable-accelerated-2d-canvas",
		"--disable-accelerated-jpeg-decoding",
		"--disable-accelerated-mjpeg-decode",
		"--disable-accelerated-video-decode",
		"--disable-gpu",
		"--disable-gpu-sandbox",
		"--disable-software-rasterizer",
		"--disable-background-media-suspend",
	}

	// 獲取 Playwright 配置
	playwrightConfig := loadPlaywrightConfig(ctx)

	browser, err := s.playWright.Chromium.Launch(playwright.BrowserTypeLaunchOptions{
		Headless: playwright.Bool(playwrightConfig.Headless),
		Args:     launchArgs,
	})
	if err != nil {
		logger().Errorf(ctx, "crawl website launch failed : %v", err)
	} else {
		defer browser.Close()
		var pageCtx playwright.BrowserContext
		var visitedURLs = make(map[string]bool)

		// 創建瀏覽器上下文，設置 User-Agent 和其他屬性
		userAgent := getRandomUserAgent(config)
		pageCtx, err = browser.NewContext(playwright.BrowserNewContextOptions{
			UserAgent: playwright.String(userAgent),
			Viewport: &playwright.Size{
				Width:  1920,
				Height: 1080,
			},
			Locale:            playwright.String("zh-TW"),
			TimezoneId:        playwright.String("Asia/Taipei"),
			JavaScriptEnabled: playwright.Bool(true),
		})

		if err != nil {
			logger().Error(ctx, "crawl website new context failed : %v", err)
		} else {
			domainUrl := ""
			domainUrl, err = extractDomainUrl(in.WebPageUrl.URL)

			vDir, _ := g.Cfg().Get(ctx, "system.temp_location", "./tmp")

			outputDir := gfile.Join(
				vDir.String(),
				in.TenantID,
				in.ServiceID,
				in.UserID,
				gbase64.EncodeString(in.WebPageUrl.URL),
			)

			vMaxDepth, _ := g.Cfg().Get(ctx, "system.crawling.max_depth", 10)
			vMaxPages, _ := g.Cfg().Get(ctx, "system.crawling.max_pages", 50)

			if err != nil {
				logger().Error(ctx, "crawl website extract domain url failed : %v", err)
			} else {
				s.crawlPage(ctx, pageCtx, in.WebPageUrl.URL, outputDir, pageToFile, domainUrl, visitedURLs, 0, vMaxDepth.Int(), vMaxPages.Int(), config)
				if !visitedURLs["https://"+domainUrl] {
					s.crawlPage(ctx, pageCtx, "https://"+domainUrl, outputDir, pageToFile, domainUrl, visitedURLs, 0, vMaxDepth.Int(), vMaxPages.Int(), config)
				}

				logger().Debugf(ctx, "Crawling completed, visiting %d pages", len(visitedURLs))
			}
		}
	}

	return
}

// Stop 實現 ICrawler 接口的 Stop 方法
func (s *sPlaywrightCrawler) Stop(ctx context.Context) {
	if s.playWright != nil {
		_ = s.playWright.Stop()
		logger().Infof(ctx, "Playwright crawler stopped")
	}
}
