package crawl

import (
	"assetManagementService/internal/consts"
	"context"
	"fmt"
	"math/rand"
	"net/url"
	"os"
	"strings"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gfile"
	"github.com/gogf/gf/v2/os/glog"
	"github.com/gogf/gf/v2/text/gstr"
)

// ErrNonWebContent 表示 URL 指向非網頁內容，應該跳過
var ErrNonWebContent = fmt.Errorf("non-web content detected, skipping URL")

// AntiBotConfig 反爬蟲配置結構體
type AntiBotConfig struct {
	MinDelay    int      `json:"min_delay"`   // 最小請求間隔（毫秒）
	MaxDelay    int      `json:"max_delay"`   // 最大請求間隔（毫秒）
	MaxRetries  int      `json:"max_retries"` // 最大重試次數
	RetryDelay  int      `json:"retry_delay"` // 重試間隔（毫秒）
	UserAgents  []string `json:"user_agents"` // User-Agent 池
	ProxyConfig struct {
		Enabled bool     `json:"enabled"` // 是否啟用代理
		Servers []string `json:"servers"` // 代理服務器列表
	} `json:"proxy"`
}

// CollyConfig Colly 專用配置結構體
type CollyConfig struct {
	ParallelRequests int      `json:"parallel_requests"` // 並發請求數
	RequestDelay     int      `json:"request_delay"`     // 請求間隔（毫秒）
	CacheEnabled     bool     `json:"cache_enabled"`     // 是否啟用緩存
	FollowRedirects  bool     `json:"follow_redirects"`  // 是否跟隨重定向
	AllowedDomains   []string `json:"allowed_domains"`   // 允許的域名
	HtmlToMarkdown   struct {
		EscapeMode        string `json:"escape_mode"`        // 轉義模式
		Domain            string `json:"domain"`             // 基礎域名
		CommonmarkEnabled bool   `json:"commonmark_enabled"` // 是否啟用 CommonMark 插件
		BaseEnabled       bool   `json:"base_enabled"`       // 是否啟用基礎插件
	} `json:"html_to_markdown"`
}

// PlaywrightConfig Playwright 專用配置結構體
type PlaywrightConfig struct {
	InstallOnInit bool `json:"install_on_init"` // 是否在初始化時安裝
	Headless      bool `json:"headless"`        // 是否無頭模式
}

// loadAntiBotConfig 載入反爬蟲配置
func loadAntiBotConfig(ctx context.Context) *AntiBotConfig {
	config := &AntiBotConfig{
		MinDelay:   1000,
		MaxDelay:   3000,
		MaxRetries: 3,
		RetryDelay: 5000,
		UserAgents: []string{
			"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
		},
	}

	// 從配置文件載入設置
	if minDelay, _ := g.Cfg().Get(ctx, "system.crawling.anti_bot.min_delay"); !minDelay.IsEmpty() {
		config.MinDelay = minDelay.Int()
	}
	if maxDelay, _ := g.Cfg().Get(ctx, "system.crawling.anti_bot.max_delay"); !maxDelay.IsEmpty() {
		config.MaxDelay = maxDelay.Int()
	}
	if maxRetries, _ := g.Cfg().Get(ctx, "system.crawling.anti_bot.max_retries"); !maxRetries.IsEmpty() {
		config.MaxRetries = maxRetries.Int()
	}
	if retryDelay, _ := g.Cfg().Get(ctx, "system.crawling.anti_bot.retry_delay"); !retryDelay.IsEmpty() {
		config.RetryDelay = retryDelay.Int()
	}
	if userAgents, _ := g.Cfg().Get(ctx, "system.crawling.anti_bot.user_agents"); !userAgents.IsEmpty() {
		config.UserAgents = userAgents.Strings()
	}
	if proxyEnabled, _ := g.Cfg().Get(ctx, "system.crawling.anti_bot.proxy.enabled"); !proxyEnabled.IsEmpty() {
		config.ProxyConfig.Enabled = proxyEnabled.Bool()
	}
	if proxyServers, _ := g.Cfg().Get(ctx, "system.crawling.anti_bot.proxy.servers"); !proxyServers.IsEmpty() {
		config.ProxyConfig.Servers = proxyServers.Strings()
	}

	return config
}

// loadCollyConfig 載入 Colly 專用配置
func loadCollyConfig(ctx context.Context) *CollyConfig {
	config := &CollyConfig{
		ParallelRequests: 2,
		RequestDelay:     1000,
		CacheEnabled:     true,
		FollowRedirects:  true,
		AllowedDomains:   []string{},
	}

	// 從配置文件載入設置
	if parallelRequests, _ := g.Cfg().Get(ctx, "system.crawling.colly.parallel_requests"); !parallelRequests.IsEmpty() {
		config.ParallelRequests = parallelRequests.Int()
	}
	if requestDelay, _ := g.Cfg().Get(ctx, "system.crawling.colly.request_delay"); !requestDelay.IsEmpty() {
		config.RequestDelay = requestDelay.Int()
	}
	if cacheEnabled, _ := g.Cfg().Get(ctx, "system.crawling.colly.cache_enabled"); !cacheEnabled.IsEmpty() {
		config.CacheEnabled = cacheEnabled.Bool()
	}
	if followRedirects, _ := g.Cfg().Get(ctx, "system.crawling.colly.follow_redirects"); !followRedirects.IsEmpty() {
		config.FollowRedirects = followRedirects.Bool()
	}
	if allowedDomains, _ := g.Cfg().Get(ctx, "system.crawling.colly.allowed_domains"); !allowedDomains.IsEmpty() {
		config.AllowedDomains = allowedDomains.Strings()
	}

	// HTML to Markdown 配置
	if escapeMode, _ := g.Cfg().Get(ctx, "system.crawling.colly.html_to_markdown.escape_mode"); !escapeMode.IsEmpty() {
		config.HtmlToMarkdown.EscapeMode = escapeMode.String()
	} else {
		config.HtmlToMarkdown.EscapeMode = "smart"
	}
	if domain, _ := g.Cfg().Get(ctx, "system.crawling.colly.html_to_markdown.domain"); !domain.IsEmpty() {
		config.HtmlToMarkdown.Domain = domain.String()
	}
	if commonmarkEnabled, _ := g.Cfg().Get(ctx, "system.crawling.colly.html_to_markdown.commonmark_enabled"); !commonmarkEnabled.IsEmpty() {
		config.HtmlToMarkdown.CommonmarkEnabled = commonmarkEnabled.Bool()
	} else {
		config.HtmlToMarkdown.CommonmarkEnabled = true
	}
	if baseEnabled, _ := g.Cfg().Get(ctx, "system.crawling.colly.html_to_markdown.base_enabled"); !baseEnabled.IsEmpty() {
		config.HtmlToMarkdown.BaseEnabled = baseEnabled.Bool()
	} else {
		config.HtmlToMarkdown.BaseEnabled = true
	}

	return config
}

// loadPlaywrightConfig 載入 Playwright 專用配置
func loadPlaywrightConfig(ctx context.Context) *PlaywrightConfig {
	config := &PlaywrightConfig{
		InstallOnInit: true,
		Headless:      true,
	}

	if installOnInit, _ := g.Cfg().Get(ctx, "system.crawling.playwright.install_on_init"); !installOnInit.IsEmpty() {
		config.InstallOnInit = installOnInit.Bool()
	}
	if headless, _ := g.Cfg().Get(ctx, "system.crawling.playwright.headless"); !headless.IsEmpty() {
		config.Headless = headless.Bool()
	}

	return config
}

// getRandomUserAgent 隨機獲取 User-Agent
func getRandomUserAgent(config *AntiBotConfig) string {
	if len(config.UserAgents) == 0 {
		return "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
	}
	return config.UserAgents[rand.Intn(len(config.UserAgents))]
}

// randomDelay 隨機延遲
func randomDelay(ctx context.Context, config *AntiBotConfig) {
	if config.MinDelay <= 0 && config.MaxDelay <= 0 {
		return
	}

	minDelay := config.MinDelay
	maxDelay := config.MaxDelay
	if minDelay > maxDelay {
		minDelay, maxDelay = maxDelay, minDelay
	}

	delay := minDelay
	if maxDelay > minDelay {
		delay = minDelay + rand.Intn(maxDelay-minDelay)
	}

	if delay > 0 {
		logger().Debugf(ctx, "Random delay: %d ms", delay)
		time.Sleep(time.Duration(delay) * time.Millisecond)
	}
}

// logger 獲取日誌記錄器
func logger() glog.ILogger {
	return g.Log().Cat(consts.CatalogCrawl)
}

// isWebContent 檢查 Content-Type 是否為可爬取的網頁內容
func isWebContent(contentType string) bool {
	if contentType == "" {
		return false
	}

	// 轉換為小寫以便比較
	contentType = strings.ToLower(strings.TrimSpace(contentType))

	// 支持的網頁內容類型
	webContentTypes := []string{
		"text/html",
		"application/xhtml+xml",
		"text/plain",
		"text/xml",
		"application/xml",
	}

	// 檢查是否匹配任何支持的類型
	for _, webType := range webContentTypes {
		if strings.HasPrefix(contentType, webType) {
			return true
		}
	}

	// 排除的非網頁內容類型
	nonWebContentTypes := []string{
		"application/zip",
		"application/x-zip-compressed",
		"application/pdf",
		"application/octet-stream",
		"image/",
		"video/",
		"audio/",
		"application/msword",
		"application/vnd.ms-excel",
		"application/vnd.ms-powerpoint",
		"application/vnd.openxmlformats",
	}

	// 檢查是否為非網頁內容
	for _, nonWebType := range nonWebContentTypes {
		if strings.HasPrefix(contentType, nonWebType) {
			return false
		}
	}

	// 默認情況下，如果不確定則認為是網頁內容
	return true
}

// extractDomainUrl 從 URL 字符串中提取域名
func extractDomainUrl(urlString string) (string, error) {
	// 驗證輸入
	if urlString == "" {
		return "", fmt.Errorf("empty URL string")
	}

	// 確保 URL 有 https:// 前綴
	if !gstr.HasPrefix(urlString, "https://") && !gstr.HasPrefix(urlString, "http://") {
		urlString = "https://" + urlString
	}

	// 解析 URL
	parsedURL, err := url.Parse(urlString)
	if err != nil {
		return "", fmt.Errorf("failed to parse URL: %w", err)
	}

	// 提取主機名
	host := parsedURL.Host
	if host == "" {
		return "", fmt.Errorf("URL has no host: %s", urlString)
	}

	// 移除端口號（如果存在）
	if i := strings.IndexByte(host, ':'); i >= 0 {
		host = host[:i]
	}

	return host, nil
}

// urlToFilename 將 URL 轉換為文件名，確保在輸出目錄中唯一
func urlToFilename(pageURL, outputDir string, mapLen int) string {
	// 驗證輸入
	if pageURL == "" {
		return gfile.Join(outputDir, fmt.Sprintf("page_%d.md", mapLen))
	}
	if outputDir == "" {
		outputDir = "."
	}

	// 確保輸出目錄存在
	if !gfile.Exists(outputDir) {
		if err := gfile.Mkdir(outputDir); err != nil {
			logger().Errorf(context.Background(), "Failed to create output directory %s: %v", outputDir, err)
			return gfile.Join(outputDir, fmt.Sprintf("page_%d.md", mapLen))
		}
	}

	// 解析 URL
	parsedURL, err := url.Parse(pageURL)
	if err != nil {
		// 如果 URL 解析失敗，使用簡單的哈希
		return gfile.Join(outputDir, fmt.Sprintf("page_%d.md", mapLen))
	}

	// 使用 URL 的路徑部分作為文件名
	urlPath := parsedURL.Path
	if urlPath == "" || urlPath == "/" {
		urlPath = "index"
	} else {
		// 移除前導斜杠並將剩餘斜杠替換為下劃線
		urlPath = strings.TrimPrefix(urlPath, "/")
		urlPath = strings.ReplaceAll(urlPath, "/", "_")
	}

	// 移除查詢參數並添加 .md 擴展名
	filename := urlPath
	if filename == "" {
		filename = "index"
	}

	// 如果文件名已存在，添加後綴
	baseFilename := gfile.Join(outputDir, filename+".md")
	finalFilename := baseFilename
	counter := 1
	maxAttempts := 1000 // 防止無限循環

	for counter < maxAttempts {
		_, err := os.Stat(finalFilename)
		if err != nil {
			if os.IsNotExist(err) {
				// 文件不存在，可以使用這個文件名
				break
			}
			// 發生其他錯誤，使用備用文件名
			logger().Errorf(context.Background(), "Error checking file existence: %v", err)
			return gfile.Join(outputDir, fmt.Sprintf("%s_%d_%d.md", filename, counter, mapLen))
		}
		// 文件存在，嘗試新的計數器
		finalFilename = gfile.Join(outputDir, fmt.Sprintf("%s_%d.md", filename, counter))
		counter++
	}

	// 如果達到最大嘗試次數，使用基於時間戳的文件名
	if counter >= maxAttempts {
		finalFilename = gfile.Join(outputDir, fmt.Sprintf("%s_%d_%d.md", filename, mapLen, time.Now().UnixNano()))
	}

	return finalFilename
}
