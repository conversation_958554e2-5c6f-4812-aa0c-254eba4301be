2025-08-14T18:34:00.206+08:00 [INFO] [assetManagementService/internal/logic/crawl.InitializeCrawlerEngine] crawler_factory.go:67: Skipping Playwright installation (not required for current engine)
2025-08-14T18:34:00.207+08:00 [INFO] [assetManagementService/internal/logic/crawl.NewCrawler] crawler_factory.go:25: Creating crawler with engine: colly
2025-08-14T18:34:00.207+08:00 [DEBU] [assetManagementService/internal/logic/crawl.(*sCollyCrawler).setupConverter] colly_crawler.go:132: HTML to Markdown converter initialized with escape mode: smart
2025-08-14T18:34:00.208+08:00 [INFO] [assetManagementService/internal/logic/crawl.newCollyCrawler] colly_crawler.go:52: Colly crawler initialized successfully
2025-08-14T18:34:00.208+08:00 [INFO] [assetManagementService/internal/logic/crawl.init.0] crawl.go:22: Crawler service registered successfully
2025-08-14T18:34:00.209+08:00 [DEBU] {38b4ea63ab9b5b18d76078429b4dadcc} [assetManagementService/internal/logic/crawl.randomDelay] common.go:386: Random delay: 163 ms
2025-08-14T18:34:00.373+08:00 [INFO] {0040b56dab9b5b18d960784216796097} [assetManagementService/internal/logic/crawl.NewCrawler] crawler_factory.go:25: Creating crawler with engine: colly
2025-08-14T18:34:00.373+08:00 [DEBU] {0040b56dab9b5b18d960784216796097} [assetManagementService/internal/logic/crawl.(*sCollyCrawler).setupConverter] colly_crawler.go:132: HTML to Markdown converter initialized with escape mode: smart
2025-08-14T18:34:00.373+08:00 [INFO] {0040b56dab9b5b18d960784216796097} [assetManagementService/internal/logic/crawl.newCollyCrawler] colly_crawler.go:52: Colly crawler initialized successfully
2025-08-14T18:34:00.373+08:00 [INFO] {0040b56dab9b5b18d960784216796097} [assetManagementService/internal/logic/crawl.NewCrawler] crawler_factory.go:25: Creating crawler with engine: colly
2025-08-14T18:34:00.374+08:00 [DEBU] {0040b56dab9b5b18d960784216796097} [assetManagementService/internal/logic/crawl.(*sCollyCrawler).setupConverter] colly_crawler.go:132: HTML to Markdown converter initialized with escape mode: smart
2025-08-14T18:34:00.374+08:00 [INFO] {0040b56dab9b5b18d960784216796097} [assetManagementService/internal/logic/crawl.newCollyCrawler] colly_crawler.go:52: Colly crawler initialized successfully
2025-08-14T18:34:00.374+08:00 [INFO] {0040b56dab9b5b18d960784216796097} [assetManagementService/internal/logic/crawl.NewCrawler] crawler_factory.go:25: Creating crawler with engine: colly
2025-08-14T18:34:00.374+08:00 [DEBU] {0040b56dab9b5b18d960784216796097} [assetManagementService/internal/logic/crawl.(*sCollyCrawler).setupConverter] colly_crawler.go:132: HTML to Markdown converter initialized with escape mode: smart
2025-08-14T18:34:00.374+08:00 [INFO] {0040b56dab9b5b18d960784216796097} [assetManagementService/internal/logic/crawl.newCollyCrawler] colly_crawler.go:52: Colly crawler initialized successfully
2025-08-14T18:34:00.374+08:00 [INFO] {0040b56dab9b5b18d960784216796097} [assetManagementService/internal/logic/crawl.NewCrawler] crawler_factory.go:25: Creating crawler with engine: colly
2025-08-14T18:34:00.374+08:00 [DEBU] {0040b56dab9b5b18d960784216796097} [assetManagementService/internal/logic/crawl.(*sCollyCrawler).setupConverter] colly_crawler.go:132: HTML to Markdown converter initialized with escape mode: smart
2025-08-14T18:34:00.374+08:00 [INFO] {0040b56dab9b5b18d960784216796097} [assetManagementService/internal/logic/crawl.newCollyCrawler] colly_crawler.go:52: Colly crawler initialized successfully
2025-08-14T18:34:00.375+08:00 [INFO] {107ddb6dab9b5b18dc607842f2978ef0} [assetManagementService/internal/logic/crawl.NewCrawler] crawler_factory.go:25: Creating crawler with engine: colly
2025-08-14T18:34:00.376+08:00 [DEBU] {107ddb6dab9b5b18dc607842f2978ef0} [assetManagementService/internal/logic/crawl.(*sCollyCrawler).setupConverter] colly_crawler.go:132: HTML to Markdown converter initialized with escape mode: smart
2025-08-14T18:34:00.376+08:00 [INFO] {107ddb6dab9b5b18dc607842f2978ef0} [assetManagementService/internal/logic/crawl.newCollyCrawler] colly_crawler.go:52: Colly crawler initialized successfully
2025-08-14T18:34:00.376+08:00 [INFO] {107ddb6dab9b5b18dc607842f2978ef0} [assetManagementService/internal/logic/crawl.NewCrawler] crawler_factory.go:25: Creating crawler with engine: colly
2025-08-14T18:34:00.376+08:00 [DEBU] {107ddb6dab9b5b18dc607842f2978ef0} [assetManagementService/internal/logic/crawl.(*sCollyCrawler).setupConverter] colly_crawler.go:132: HTML to Markdown converter initialized with escape mode: smart
2025-08-14T18:34:00.376+08:00 [INFO] {107ddb6dab9b5b18dc607842f2978ef0} [assetManagementService/internal/logic/crawl.newCollyCrawler] colly_crawler.go:52: Colly crawler initialized successfully
2025-08-14T18:34:00.376+08:00 [INFO] {a02fe46dab9b5b18dd607842d9290176} [assetManagementService/internal/logic/crawl.NewCrawler] crawler_factory.go:25: Creating crawler with engine: colly
2025-08-14T18:34:00.376+08:00 [DEBU] {a02fe46dab9b5b18dd607842d9290176} [assetManagementService/internal/logic/crawl.(*sCollyCrawler).setupConverter] colly_crawler.go:132: HTML to Markdown converter initialized with escape mode: smart
2025-08-14T18:34:00.376+08:00 [INFO] {a02fe46dab9b5b18dd607842d9290176} [assetManagementService/internal/logic/crawl.newCollyCrawler] colly_crawler.go:52: Colly crawler initialized successfully
