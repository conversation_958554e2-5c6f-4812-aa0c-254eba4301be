package crawl

import (
	"assetManagementService/internal/service"
	"os"
	"testing"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gcfg"
	"github.com/gogf/gf/v2/os/gctx"
	"github.com/gogf/gf/v2/test/gtest"
)

func TestAntiBotConfig(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := gctx.New()

		// 測試載入反爬蟲配置
		config := loadAntiBotConfig(ctx)
		t.AssertNE(config, nil)
		t.AssertGT(config.MinDelay, 0)
		t.AssertGT(config.MaxDelay, 0)
		t.AssertGT(config.MaxRetries, 0)
		t.AssertGT(len(config.UserAgents), 0)
	})
}

func TestGetRandomUserAgent(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		config := &AntiBotConfig{
			UserAgents: []string{
				"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
				"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36",
			},
		}

		// 測試隨機獲取 User-Agent
		userAgent1 := getRandomUserAgent(config)
		userAgent2 := getRandomUserAgent(config)

		t.AssertNE(userAgent1, "")
		t.AssertNE(userAgent2, "")
		t.AssertIN(userAgent1, config.UserAgents)
		t.AssertIN(userAgent2, config.UserAgents)
	})
}

func TestRandomDelay(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := gctx.New()
		config := &AntiBotConfig{
			MinDelay: 100,
			MaxDelay: 200,
		}

		// 測試隨機延遲功能
		start := time.Now()
		randomDelay(ctx, config)
		elapsed := time.Since(start)

		// 延遲應該在配置範圍內
		t.AssertGE(elapsed.Milliseconds(), int64(config.MinDelay))
		t.AssertLE(elapsed.Milliseconds(), int64(config.MaxDelay+50)) // 允許一些誤差
	})
}

func TestExtractDomainUrl(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {

		// 測試域名提取功能
		testCases := []struct {
			input    string
			expected string
		}{
			{"https://www.example.com/path", "www.example.com"},
			{"http://example.com:8080/path", "example.com"},
			{"example.com", "example.com"},
			{"www.example.com/path?query=1", "www.example.com"},
		}

		for _, tc := range testCases {
			result, err := extractDomainUrl(tc.input)
			t.AssertNil(err)
			t.AssertEQ(result, tc.expected)
		}
	})
}

func TestAntiBotConfigFromConfig(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := gctx.New()

		// 設置測試配置
		g.Cfg().GetAdapter().(*gcfg.AdapterFile).SetContent(`
system:
  crawling:
    anti_bot:
      min_delay: 2000
      max_delay: 5000
      max_retries: 5
      retry_delay: 10000
      user_agents:
        - "Test User Agent 1"
        - "Test User Agent 2"
      proxy:
        enabled: true
        servers:
          - "http://proxy1:8080"
          - "http://proxy2:8080"
`)

		config := loadAntiBotConfig(ctx)

		// 驗證配置是否正確載入
		t.AssertEQ(config.MinDelay, 2000)
		t.AssertEQ(config.MaxDelay, 5000)
		t.AssertEQ(config.MaxRetries, 5)
		t.AssertEQ(config.RetryDelay, 10000)
		t.AssertEQ(len(config.UserAgents), 2)
		t.AssertEQ(config.UserAgents[0], "Test User Agent 1")
		t.AssertEQ(config.ProxyConfig.Enabled, true)
		t.AssertEQ(len(config.ProxyConfig.Servers), 2)
	})
}

// TestIsWebContent 測試內容類型檢查功能
func TestIsWebContent(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {

		// 測試案例
		testCases := []struct {
			contentType string
			expected    bool
			description string
		}{
			// 應該被識別為網頁內容的類型
			{"text/html", true, "HTML 內容"},
			{"text/html; charset=utf-8", true, "HTML 內容帶字符集"},
			{"application/xhtml+xml", true, "XHTML 內容"},
			{"text/plain", true, "純文本內容"},
			{"text/xml", true, "XML 內容"},
			{"application/xml", true, "應用程序 XML"},

			// 應該被跳過的非網頁內容類型
			{"application/zip", false, "ZIP 壓縮檔"},
			{"application/x-zip-compressed", false, "ZIP 壓縮檔（另一種格式）"},
			{"application/pdf", false, "PDF 文件"},
			{"application/octet-stream", false, "二進制流"},
			{"image/jpeg", false, "JPEG 圖片"},
			{"image/png", false, "PNG 圖片"},
			{"video/mp4", false, "MP4 視頻"},
			{"audio/mpeg", false, "MP3 音頻"},
			{"application/msword", false, "Word 文檔"},
			{"application/vnd.ms-excel", false, "Excel 文檔"},
			{"application/vnd.openxmlformats-officedocument.wordprocessingml.document", false, "DOCX 文檔"},

			// 邊界案例
			{"", false, "空字符串"},
			{"TEXT/HTML", true, "大寫 HTML"},
			{"  text/html  ", true, "帶空格的 HTML"},
			{"unknown/type", true, "未知類型（默認允許）"},
		}

		for _, tc := range testCases {
			result := isWebContent(tc.contentType)
			if result != tc.expected {
				t.Errorf("isWebContent(%q) = %v, expected %v (%s)",
					tc.contentType, result, tc.expected, tc.description)
			}
		}
	})
}

// TestCrawlerFactory 測試爬蟲工廠的配置切換功能
func TestCrawlerFactory(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := gctx.New()

		// 測試默認配置（應該返回 Playwright 爬蟲）
		crawler := NewCrawler(ctx)
		t.AssertNE(crawler, nil)

		// 測試配置為 colly
		g.Cfg().MustGet(ctx, "system.crawling.engine").Set("colly")
		collyCrawler := NewCrawler(ctx)
		t.AssertNE(collyCrawler, nil)

		// 測試配置為 playwright
		g.Cfg().MustGet(ctx, "system.crawling.engine").Set("playwright")
		playwrightCrawler := NewCrawler(ctx)
		t.AssertNE(playwrightCrawler, nil)

		// 測試無效配置（應該回退到 playwright）
		g.Cfg().MustGet(ctx, "system.crawling.engine").Set("invalid")
		fallbackCrawler := NewCrawler(ctx)
		t.AssertNE(fallbackCrawler, nil)
	})
}

// TestNewConfigLoading 測試新的配置載入功能
func TestNewConfigLoading(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := gctx.New()

		// 測試反爬蟲配置載入
		antiBotConfig := loadAntiBotConfig(ctx)
		t.AssertNE(antiBotConfig, nil)
		t.AssertGT(antiBotConfig.MinDelay, 0)
		t.AssertGT(antiBotConfig.MaxDelay, 0)
		t.AssertGT(antiBotConfig.MaxRetries, 0)
		t.AssertGT(len(antiBotConfig.UserAgents), 0)

		// 測試 Colly 配置載入
		collyConfig := loadCollyConfig(ctx)
		t.AssertNE(collyConfig, nil)
		t.AssertGT(collyConfig.ParallelRequests, 0)
		t.AssertGT(collyConfig.RequestDelay, 0)
		t.AssertNE(collyConfig.HtmlToMarkdown.EscapeMode, "")

		// 測試 Playwright 配置載入
		playwrightConfig := loadPlaywrightConfig(ctx)
		t.AssertNE(playwrightConfig, nil)
	})
}

// TestUtilityFunctions 測試工具函數
func TestUtilityFunctions(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {

		// 測試域名提取
		domain, err := extractDomainUrl("https://example.com/path?query=1")
		t.AssertNil(err)
		t.AssertEQ(domain, "example.com")

		domain, err = extractDomainUrl("example.com")
		t.AssertNil(err)
		t.AssertEQ(domain, "example.com")

		// 測試無效 URL
		_, err = extractDomainUrl("")
		t.AssertNE(err, nil)

		// 測試內容類型檢查
		t.AssertEQ(isWebContent("text/html"), true)
		t.AssertEQ(isWebContent("application/xhtml+xml"), true)
		t.AssertEQ(isWebContent("application/pdf"), false)
		t.AssertEQ(isWebContent("image/jpeg"), false)
		t.AssertEQ(isWebContent(""), false)

		// 測試隨機 User-Agent
		config := &AntiBotConfig{
			UserAgents: []string{"Agent1", "Agent2", "Agent3"},
		}
		userAgent := getRandomUserAgent(config)
		t.AssertIN(userAgent, []string{"Agent1", "Agent2", "Agent3"})

		// 測試空 User-Agent 列表
		emptyConfig := &AntiBotConfig{UserAgents: []string{}}
		defaultAgent := getRandomUserAgent(emptyConfig)
		t.AssertGT(len(defaultAgent), 0)

		// 測試文件名生成
		filename := urlToFilename("https://example.com/test", "/tmp", 1)
		t.AssertGT(len(filename), 0)
	})
}

// TestPlaywrightInstallCondition 測試 Playwright 條件性安裝
func TestPlaywrightInstallCondition(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := gctx.New()

		// 測試當引擎為 playwright 時應該安裝
		g.Cfg().MustGet(ctx, "system.crawling.engine").Set("playwright")
		g.Cfg().MustGet(ctx, "system.crawling.playwright.install_on_init").Set(true)
		shouldInstall := shouldInstallPlaywright(ctx)
		t.AssertEQ(shouldInstall, true)

		// 測試當引擎為 colly 時不應該安裝
		g.Cfg().MustGet(ctx, "system.crawling.engine").Set("colly")
		shouldInstall = shouldInstallPlaywright(ctx)
		t.AssertEQ(shouldInstall, false)

		// 測試當 install_on_init 為 false 時不應該安裝
		g.Cfg().MustGet(ctx, "system.crawling.engine").Set("playwright")
		g.Cfg().MustGet(ctx, "system.crawling.playwright.install_on_init").Set(false)
		shouldInstall = shouldInstallPlaywright(ctx)
		t.AssertEQ(shouldInstall, false)
	})
}

// TestCrawlerInterface 測試爬蟲接口實現
func TestCrawlerInterface(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := gctx.New()

		// 測試 Colly 爬蟲實現接口
		g.Cfg().MustGet(ctx, "system.crawling.engine").Set("colly")
		collyCrawler := NewCrawler(ctx)
		t.AssertNE(collyCrawler, nil)

		// 驗證實現了 ICrawler 接口
		var _ service.ICrawler = collyCrawler

		// 測試 Playwright 爬蟲實現接口
		g.Cfg().MustGet(ctx, "system.crawling.engine").Set("playwright")
		playwrightCrawler := NewCrawler(ctx)
		t.AssertNE(playwrightCrawler, nil)

		// 驗證實現了 ICrawler 接口
		var _ service.ICrawler = playwrightCrawler
	})
}

// TestCrawlerStop 測試爬蟲停止功能
func TestCrawlerStop(t *testing.T) {
	gtest.C(t, func(t *gtest.T) {
		ctx := gctx.New()

		// 測試 Colly 爬蟲停止
		g.Cfg().MustGet(ctx, "system.crawling.engine").Set("colly")
		collyCrawler := NewCrawler(ctx)
		t.AssertNE(collyCrawler, nil)

		// 停止應該不會 panic
		t.AssertNil(func() {
			collyCrawler.Stop(ctx)
		})

		// 測試 Playwright 爬蟲停止
		g.Cfg().MustGet(ctx, "system.crawling.engine").Set("playwright")
		playwrightCrawler := NewCrawler(ctx)
		t.AssertNE(playwrightCrawler, nil)

		// 停止應該不會 panic
		t.AssertNil(func() {
			playwrightCrawler.Stop(ctx)
		})
	})
}

// TestMain 測試主函數，設置測試環境
func TestMain(m *testing.M) {
	// 設置測試配置
	os.Setenv("GF_GCFG_FILE", "manifest/config/configToNacos.yaml")

	// 運行測試
	code := m.Run()

	// 清理
	os.Exit(code)
}
