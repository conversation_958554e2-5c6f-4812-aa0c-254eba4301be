# 資產管理服務 API 規格文檔

## 服務概述

**服務名稱**: Asset Management Service (AMS)  
**版本**: v1  
**基礎URL**: `http://localhost:8086`  
**服務標識**: `ams.svc`  
**協議**: HTTP/HTTPS  
**數據格式**: JSON  

## 服務架構

本服務基於 GoFrame v2 框架開發，採用微服務架構，使用 Nacos 作為配置中心和服務註冊發現。支援多種資源類型的管理，包括文件、網站URL、YouTube鏈接和純文本內容。

### 技術棧
- **框架**: GoFrame v2
- **配置中心**: Nacos
- **消息隊列**: RabbitMQ
- **存儲**: 本地文件系統 / Google Cloud Storage
- **網頁爬取**: Playwright / Colly（可配置切換）
- **數據庫**: MySQL (通過 data service 訪問)

## 通用規範

### 請求頭
```
Content-Type: application/json
X-SERVICE: ams.svc
```

### 基礎請求參數 (BaseInfo)
所有API請求都需要包含以下基礎參數：

```json
{
  "tenant_id": "string",    // 必填，租戶ID
  "service_id": "string",   // 必填，服務ID  
  "user_id": "string"       // 必填，用戶ID
}
```

### 通用響應格式 (BaseRes)
```json
{
  "code": 0,              // 狀態碼，0表示成功
  "message": "success",   // 響應消息
  "cost": "10ms"         // 請求耗時
}
```

### 錯誤碼定義
| 錯誤碼 | 說明 |
|--------|------|
| 0 | 成功 |
| -1 | 失敗 |
| -1001 | 文件未找到 |

## API 端點詳細說明

### 0. 資產查詢

#### 0.1 獲取所有資產
**端點**: `POST /v1/attachments/getAssets`
**描述**: 獲取指定租戶和服務下的所有資產記錄

**請求參數**:
```json
{
  "tenant_id": "string",      // 必填，租戶ID
  "service_id": "string",     // 必填，服務ID
  "user_id": "string"         // 必填，用戶ID
}
```

**響應**:
```json
{
  "code": 0,
  "message": "success",
  "cost": "50ms",
  "assets": [                 // 資產記錄列表
    {
      "schema": "tenant_001",
      "table": "service_001_resources",
      "service_id": "service_001",
      "id": "12345",
      "upload_user_id": "user_001",
      "create_at": "2025-01-11T10:30:00Z",
      "update_at": "2025-01-11T10:30:00Z",
      "file_storage_provider": "gcs",
      "upload_files": [],
      "url_contents": [],
      "youtube_contents": [],
      "plain_text_contents": []
    }
  ]
}
```

### 1. 文件管理

#### 1.1 上傳文件
**端點**: `POST /v1/attachments/upload`  
**描述**: 上傳一個或多個文件到系統中  
**Content-Type**: `multipart/form-data`

**請求參數**:
```json
{
  "file": "file[]",           // 必填，上傳的文件(支援多文件)
  "tenant_id": "string",      // 必填，租戶ID
  "service_id": "string",     // 必填，服務ID
  "user_id": "string",        // 必填，用戶ID
  "content_type": "string"    // 必填，內容類型，多個用逗號分隔
}
```

**注意**: 實際的字段名稱在代碼中為 `File`（而非 `Attachments`）

**響應**:
```json
{
  "code": 0,
  "message": "success",
  "cost": "100ms",
  "file_names": [             // 上傳成功的文件名列表
    "file1.pdf",
    "file2.jpg"
  ]
}
```

**支援的文件類型**: 根據 content_type 參數決定  
**文件大小限制**: 由系統配置決定  
**存儲位置**: 支援本地文件系統和Google Cloud Storage

#### 1.2 下載文件
**端點**: `GET /v1/attachments/download`  
**描述**: 下載指定路徑的文件（僅支援本地存儲的文件）

**請求參數**:
```json
{
  "file_path": "string"       // 必填，文件路徑
}
```

**響應**: 直接返回文件流或錯誤信息
```json
{
  "code": 0,
  "message": "success",
  "cost": "50ms"
}
```

**注意**: GCS存儲的文件需要通過GCS API直接下載

#### 1.3 刪除文件
**端點**: `POST /v1/attachments/deleteFile`  
**描述**: 刪除指定的文件

**請求參數**:
```json
{
  "tenant_id": "string",      // 必填，租戶ID
  "service_id": "string",     // 必填，服務ID
  "user_id": "string",        // 必填，用戶ID
  "file_names": [             // 必填，要刪除的文件名列表
    "file1.pdf",
    "file2.jpg"
  ]
}
```

**響應**:
```json
{
  "code": 0,
  "message": "success",
  "cost": "80ms"
}
```

### 2. 網站URL管理

#### 2.1 設置網站URL
**端點**: `POST /v1/attachments/setWebSite`  
**描述**: 添加網站URL並觸發網頁爬取

**請求參數**:
```json
{
  "tenant_id": "string",      // 必填，租戶ID
  "service_id": "string",     // 必填，服務ID
  "user_id": "string",        // 必填，用戶ID
  "web_sites": [              // 必填，網站URL列表
    {
      "url": "https://example.com",     // 必填，網站URL
      "remark": "示例網站"              // 可選，備註
    }
  ]
}
```

**響應**:
```json
{
  "code": 0,
  "message": "success",
  "cost": "200ms"
}
```

**功能說明**:
- 系統支援兩種爬蟲引擎：Playwright 和 Colly（可通過配置切換）
- 支援設置最大爬取深度和頁面數量
- 爬取的內容會轉換為Markdown格式存儲
- 兩種引擎提供相同的功能和輸出格式，可根據需求選擇

#### 2.2 刪除網站URL
**端點**: `POST /v1/attachments/deleteWebSite`  
**描述**: 刪除指定的網站URL及其相關數據

**請求參數**:
```json
{
  "tenant_id": "string",      // 必填，租戶ID
  "service_id": "string",     // 必填，服務ID
  "user_id": "string",        // 必填，用戶ID
  "web_site": [               // 必填，要刪除的網站URL列表
    "https://example.com"
  ]
}
```

**響應**:
```json
{
  "code": 0,
  "message": "success",
  "cost": "150ms"
}
```

### 3. YouTube鏈接管理

#### 3.1 設置YouTube鏈接
**端點**: `POST /v1/attachments/setYoutubeLink`  
**描述**: 添加YouTube鏈接到系統中

**請求參數**:
```json
{
  "tenant_id": "string",      // 必填，租戶ID
  "service_id": "string",     // 必填，服務ID
  "user_id": "string",        // 必填，用戶ID
  "youtube_contents": [       // 必填，YouTube內容列表
    {
      "youtube_link": "https://youtube.com/watch?v=xxx",  // 必填，YouTube鏈接
      "remark": "教學影片"                                 // 可選，備註
    }
  ]
}
```

**響應**:
```json
{
  "code": 0,
  "message": "success",
  "cost": "100ms"
}
```

#### 3.2 刪除YouTube鏈接
**端點**: `POST /v1/attachments/deleteYoutubeLink`
**描述**: 刪除指定的YouTube鏈接

**請求參數**:
```json
{
  "tenant_id": "string",      // 必填，租戶ID
  "service_id": "string",     // 必填，服務ID
  "user_id": "string",        // 必填，用戶ID
  "youtube_links": [          // 必填，要刪除的YouTube鏈接列表
    "https://youtube.com/watch?v=xxx"
  ]
}
```

**注意**: 端點名稱使用單數形式 `deleteYoutubeLink`，但參數為複數 `youtube_links`

**響應**:
```json
{
  "code": 0,
  "message": "success",
  "cost": "80ms"
}
```

### 4. 純文本內容管理

#### 4.1 設置純文本內容
**端點**: `POST /v1/attachments/setPlainText`  
**描述**: 添加純文本內容到系統中

**請求參數**:
```json
{
  "tenant_id": "string",      // 必填，租戶ID
  "service_id": "string",     // 必填，服務ID
  "user_id": "string",        // 必填，用戶ID
  "content": "string",        // 必填，純文本內容
  "remark": "string"          // 可選，備註
}
```

**響應**:
```json
{
  "code": 0,
  "message": "success",
  "cost": "50ms"
}
```

#### 4.2 刪除純文本內容
**端點**: `POST /v1/attachments/deletePlainText`  
**描述**: 刪除指定的純文本內容

**請求參數**:
```json
{
  "tenant_id": "string",      // 必填，租戶ID
  "service_id": "string",     // 必填，服務ID
  "user_id": "string",        // 必填，用戶ID
  "content": "string"         // 必填，要刪除的純文本內容
}
```

**響應**:
```json
{
  "code": 0,
  "message": "success",
  "cost": "60ms"
}
```

## 數據模型

### 資源記錄 (ResourceRecord)
```json
{
  "schema": "string",                    // 數據庫schema (tenant_id)
  "table": "string",                     // 表名
  "service_id": "string",                // 服務ID
  "id": "string",                        // 記錄ID
  "upload_user_id": "string",            // 上傳用戶ID
  "create_at": "2025-01-11T10:30:00Z",   // 創建時間戳 (time.Time格式)
  "update_at": "2025-01-11T10:30:00Z",   // 更新時間戳 (time.Time格式)
  "file_storage_provider": "string",     // 存儲提供商 (localFS/gcs)
  "upload_files": [],                    // 上傳文件列表
  "url_contents": [],                    // 網站URL內容
  "youtube_contents": [],                // YouTube內容
  "plain_text_contents": []              // 純文本內容
}
```

**注意**: `create_at` 和 `update_at` 字段在代碼中使用 `time.Time` 類型，JSON 序列化為 ISO 8601 格式

### 文件內容 (FileContent)
```json
{
  "file_name": "string",        // 文件名
  "content_type": "string",     // MIME類型
  "size": 1024,                 // 文件大小(字節)
  "storage_bucket": "string",   // 存儲桶名稱
  "access_url": "string",       // 訪問URL
  "access_path": "string"       // 訪問路徑
}
```

### 網站URL內容 (WebSiteURLContent)
```json
{
  "website_url": "string",              // 網站URL
  "page_to_file_content": {             // 頁面到文件內容的映射
    "page_url": {
      "file_name": "string",
      "content_type": "string",
      "size": 1024,
      "storage_bucket": "string",
      "access_url": "string",
      "access_path": "string"
    }
  }
}
```

## 系統配置

### 存儲配置
```yaml
storage:
  provider: gcs                    # 存儲提供商: localFS 或 gcs
  localFS:
    path: ./files                  # 本地存儲路徑
  gcs:
    credential_file: ./key.json    # GCS憑證文件
    bucket: dev-123                # GCS存儲桶
```

### 爬取配置
```yaml
system:
  crawling:
    # 爬蟲引擎選擇：colly 或 playwright（默認 playwright 確保向後兼容）
    engine: "playwright"

    # 通用配置（兩個引擎共用）
    max_depth: 10                  # 最大爬取深度
    max_pages: 50                  # 最大爬取頁面數

    # Colly 專用配置
    colly:
      parallel_requests: 2         # 並發請求數
      request_delay: 1000          # 請求間隔（毫秒）
      cache_enabled: true          # 是否啟用緩存
      follow_redirects: true       # 是否跟隨重定向
      allowed_domains: []          # 允許的域名（空表示不限制）
      html_to_markdown:
        escape_mode: "smart"       # 轉義模式：smart（智能）、disabled（禁用）
        domain: ""                 # 用於相對鏈接轉換的基礎域名
        commonmark_enabled: true   # 是否啟用 CommonMark 插件
        base_enabled: true         # 是否啟用基礎插件

    # Playwright 專用配置
    playwright:
      install_on_init: true        # 是否在初始化時安裝（只有選擇 playwright 時才安裝）
      headless: true               # 是否無頭模式

    # 反爬蟲配置（兩個引擎共用）
    anti_bot:
      min_delay: 1000              # 最小請求間隔（毫秒）
      max_delay: 3000              # 最大請求間隔（毫秒）
      max_retries: 3               # 最大重試次數
      retry_delay: 5000            # 重試間隔（毫秒）
      user_agents:                 # User-Agent 池
        - "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        - "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
      proxy:                       # 代理配置（可選）
        enabled: false             # 是否啟用代理
        servers: []                # 代理服務器列表
```

### 服務配置
```yaml
server:
  address: ":8086"                 # 服務監聽地址
  openapiPath: "/api.json"         # OpenAPI文檔路徑
  swaggerPath: "/swagger"          # Swagger UI路徑

# 數據服務配置
data_service:
  name: dsh.svc                    # 數據服務名稱
  scheme: http                     # 數據服務協議

# 消息隊列配置
rabbitMQ:
  url: "************************************/"  # RabbitMQ連接URL

# 系統配置
system:
  temp_location: ./tmp             # 臨時文件位置
  crawling:
    max_depth: 10                  # 最大爬取深度
    max_pages: 50                  # 最大爬取頁面數
```

## 爬蟲引擎選擇

系統支援兩種爬蟲引擎，可通過配置文件進行切換：

### Playwright 引擎
**適用場景**：
- 需要執行 JavaScript 的動態網站
- 需要模擬真實瀏覽器行為的場景
- 對反爬蟲檢測要求較高的網站

**特點**：
- 使用真實的 Chromium 瀏覽器內核
- 支援 JavaScript 執行和動態內容載入
- 提供強大的反爬蟲規避能力
- 資源消耗較高，但功能最全面

### Colly 引擎
**適用場景**：
- 靜態網站或簡單的動態網站
- 需要高性能大規模爬取的場景
- 資源受限的環境

**特點**：
- 輕量級 HTTP 客戶端，性能優異
- 資源消耗低，併發能力強
- 內建緩存和重試機制
- 不支援 JavaScript 執行

### 引擎切換
通過修改配置文件中的 `system.crawling.engine` 參數即可切換引擎：
```yaml
system:
  crawling:
    engine: "colly"      # 使用 Colly 引擎
    # engine: "playwright" # 使用 Playwright 引擎（默認）
```

**注意事項**：
- 只有當配置為 "playwright" 時，系統才會在初始化階段安裝 Playwright
- 兩個引擎提供相同的 API 接口和輸出格式
- 可以根據實際需求在不同環境中使用不同的引擎

## 反爬蟲策略

系統實施了多層反爬蟲策略來規避目標網站的反爬蟲機制：

### 瀏覽器偽裝
- **真實瀏覽器模擬**：Playwright 引擎使用真實的 Chromium 瀏覽器
- **HTTP 客戶端模擬**：Colly 引擎模擬真實的瀏覽器請求行為
- **User-Agent 輪換**：從配置的 User-Agent 池中隨機選擇
- **瀏覽器特徵設置**：設置真實的視窗大小、語言、時區等屬性
- **HTTP Headers**：添加完整的瀏覽器 headers（Accept、Accept-Language、Sec-Fetch-* 等）

### 行為模擬
- **隨機延遲**：在請求間添加隨機延遲（可配置範圍）
- **請求頻率控制**：避免過於頻繁的請求觸發反爬蟲機制
- **真實瀏覽器行為**：等待頁面完全載入（networkidle 狀態）

### 錯誤處理與重試
- **智能重試**：針對 403/429 錯誤實施指數退避重試
- **錯誤分類**：區分不同類型的錯誤並採取相應策略
- **詳細日誌**：記錄爬取過程中的詳細信息便於調試

### 技術實現
- **增強啟動參數**：使用多個 Chromium 參數來規避自動化檢測
- **上下文隔離**：每個爬取任務使用獨立的瀏覽器上下文
- **資源優化**：禁用不必要的功能以提高性能和隱蔽性

### 配置參數說明
- `min_delay/max_delay`：控制請求間隔的隨機範圍
- `max_retries`：遇到錯誤時的最大重試次數
- `retry_delay`：重試間的等待時間
- `user_agents`：可用的 User-Agent 字符串池
- `proxy`：代理服務器配置（支持輪換）

## 安全性

### 認證授權
- 所有API請求都需要包含有效的租戶ID、服務ID和用戶ID
- 系統通過這些參數進行多租戶隔離和權限控制

### 數據隔離
- 每個租戶的數據存儲在獨立的數據庫schema中
- 表名格式: `{service_id}_resources`
- 數據庫表結構自動創建，包含以下字段：
  - `id`: 主鍵，自增長整數
  - `service_id`: 服務ID
  - `upload_user_id`: 上傳用戶ID
  - `create_at`: 創建時間戳
  - `update_at`: 更新時間戳
  - `file_storage_provider`: 存儲提供商
  - `upload_files`: JSON格式的上傳文件信息
  - `url_contents`: JSON格式的網站URL內容
  - `youtube_contents`: JSON格式的YouTube內容
  - `plain_text_contents`: JSON格式的純文本內容

## 限制說明

1. **文件上傳**: 文件大小和類型限制由系統配置決定
2. **網頁爬取**: 受max_depth和max_pages參數限制
3. **併發處理**: 使用RabbitMQ進行異步處理，避免阻塞
4. **存儲**: 支援本地文件系統和Google Cloud Storage

## 錯誤處理

### 常見錯誤場景
1. **參數驗證失敗**: 缺少必填參數或參數格式錯誤
2. **文件操作失敗**: 文件不存在、權限不足或存儲空間不足
3. **網絡錯誤**: 網頁爬取失敗或外部服務不可用
4. **數據庫錯誤**: 數據保存或查詢失敗

### 錯誤響應格式
```json
{
  "code": -1,
  "message": "具體錯誤信息",
  "cost": "10ms"
}
```

## 監控和日誌

### 日誌分類
- `attachments_controller`: 控制器層日誌
- `storage`: 存儲操作日誌
- `MQ`: 消息隊列日誌
- `crawl`: 網頁爬取日誌
- `http_logs`: HTTP請求日誌

### 日誌配置
```yaml
logger:
  level: "all"                     # 日誌級別
  stdout: true                     # 是否輸出到控制台
  path: ./logs                     # 日誌文件路徑
  file: ams_{Y-m-d}.log           # 日誌文件名格式
  RotateExpire: "1d"              # 日誌輪轉週期
  RotateBackupLimit: 1            # 保留備份數量
  RotateBackupExpire: "7d"        # 備份過期時間
  RotateBackupCompress: 9         # 壓縮級別
```

### 性能監控
- 每個API響應都包含執行耗時(cost)
- 支援通過日誌追蹤請求處理過程

## 部署信息

### 服務註冊
- 服務名: `ams.svc`
- 註冊中心: Nacos
- 健康檢查: 支援

### 容器化部署
- 支援Docker容器化部署
- Kubernetes部署配置位於 `manifest/deploy/` 目錄
- 支援 Kustomize 進行多環境部署管理
- 部署環境：
  - `develop`: 開發環境
  - `production`: 生產環境

### 開發工具
- 使用 GoFrame CLI 工具進行代碼生成
- 支援自動生成控制器、服務、DAO等代碼
- 提供 Makefile 進行構建和部署自動化

### 服務發現與負載均衡
- 使用 Nacos 進行服務註冊與發現
- 支援輪詢負載均衡策略
- 自動健康檢查和故障轉移

---

## 重構更新記錄

### v1.1 (2025-01-11)
- **新增功能**:
  - 新增 `GET /v1/attachments/getAssets` API 端點用於獲取資產列表
  - 重構數據服務請求邏輯，提取通用的 `sendDataServiceRequest` 方法
  - 重構表創建邏輯，提取 `ensureTableExists` 方法

- **代碼優化**:
  - 消除重複的HTTP請求代碼
  - 提升代碼維護性和可讀性
  - 統一錯誤處理和日誌記錄

- **修復問題**:
  - 修復 YouTube 刪除方法名不匹配問題
  - 修復編譯錯誤和未定義引用問題

### v1.0 (2025-01-09)
- 初始版本發布
- 實現基礎的資產管理功能

---

**文檔版本**: v1.1
**最後更新**: 2025-01-11
**維護者**: Asset Management Service Team
